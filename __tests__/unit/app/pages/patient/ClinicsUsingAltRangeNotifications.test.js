/* global jest */
/* global expect */
/* global describe */
/* global afterEach */
/* global it */
/* global beforeEach */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import configureStore from 'redux-mock-store';
import { Provider } from 'react-redux';
import thunk from 'redux-thunk';
import { MMOLL_UNITS, MGDL_UNITS } from '@app/core/constants';

import ClinicsUsingAltRangeNotifications from '@app/pages/patient/ClinicsUsingAltRangeNotifications';
import userEvent from '@testing-library/user-event';

const mockStore = configureStore([thunk]);

// export const GLYCEMIC_RANGE = {
//   ADA_STANDARD: 'ADA standard',
//   ADA_OLDER_HIGH_RISK: 'ADA older or high-risk',
//   ADA_PREGNANCY_T1: 'ADA pregnancy type 1',
//   ADA_GESTATIONAL_T2: 'ADA pregnancy GDM or type 2',
// };

describe('ClinicsUsingAltRangeNotifications', () => {
  describe('when multiple clinics have non-standard ranges applied', () => {
    const api = { clinics: { getClinicsForPatient: jest.fn() } };

    const state = {
      blip: {
        loggedInUserId: '1234',
        currentPatientInViewId: '1234',
        allUsersMap: { '1234': { userid: '1234' } },
        clinics: {
          '1111': {
            name: 'First Clinic',
            patients: { '1234': { glycemicRanges: 'ADA pregnancy type 1' } },
          },
          '2222': {
            name: 'Second Clinic',
            patients: { '1234': { glycemicRanges: 'ADA standard' } },
          },
          '3333': {
            name: 'Third Clinic',
            patients: { '1234': { glycemicRanges: 'ADA older or high-risk' } },
          },
          '4444': {
            name: 'Fourth Clinic',
            patients: { '1234': { glycemicRanges: 'ADA pregnancy GDM or type 2' } },
          },
        },
        working: {
          fetchingClinicsForPatient: {},
        },
      },
    };

    const store = mockStore(state);

    it('renders a notification for each clinic with non-standard ranges', () => {
      render(
        <Provider store={store}>
          <ClinicsUsingAltRangeNotifications api={api} />
        </Provider>
      );

      expect(screen.getAllByText(/Non-Standard Target Range/).length).toBe(2);
      expect(screen.getBy)
    });
  });
});
