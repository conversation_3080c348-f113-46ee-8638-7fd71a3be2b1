/**
 * Copyright (c) 2017, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

.PatientSettings {
  margin-top: 30px;

  &.no-edit {
    a.PatientSettings-reset {
      display: none;
    }
  }
}

.PatientSettings-blocks {
  margin-bottom: @spacing-base;
  float: left;
}

.PatientSettings-blocks--full-width {
  width: 100%;
}

.PatientSettings-bgValue {
  margin: 0 15px;
  font-weight: bold;
}

a.PatientSettings-reset {
  color: @gray-dark;
  text-decoration: none;
  margin-left: @spacing-small;

  &:hover {
    color: @blue-green-light;
  }

  &:active {
    color: @blue-green;
  }
}

.PatientSettings-error-message {
  color: @red-error;
  margin: 0 0 30px;
}
