/*
 * == BSD2 LICENSE ==
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 * == BSD2 LICENSE ==
 */

// tooltips

// cbg
g.svg-tooltip-cbg {
  opacity: 0.7;
  &.d3-bg-low path.tooltip-outline {
    fill: @bg-low;
  }
  &.d3-bg-target path.tooltip-outline {
    fill: @bg-target;
  }
  &.d3-bg-high path.tooltip-outline {
    fill: @bg-high;
  }
}

// basal
g.svg-tooltip-basal {
  polygon {
    fill: white;
    opacity: 0.8;
    stroke: @basal;
    stroke-width: 1.5;
  }
  &.d3-basal-undelivered polygon {
    stroke-dasharray: 1.5,3;
    stroke-linecap: round;
  }
}

// smbg
g.svg-tooltip-smbg {
  polygon {
    fill: white;
    opacity: 0.7;
    stroke-width: 1.5;
  }
  &.d3-bg-low {
    stroke: @bg-low;
  }
  &.d3-bg-target {
    stroke: @bg-target;
  }
  &.d3-bg-high {
    stroke: @bg-high;
  }
}

// range (& mean) overlays in trends view
g.svg-tooltip-range {
  polygon {
    fill: @fill-lightest;
    opacity: 0.7;
    stroke-width: 1.5;
    stroke-opacity: 0.4;
    stroke: @trends-range;
  }
}

// generic = bolus(/wizard)
g.svg-tooltip-generic {
  polygon {
    fill: none;
    stroke-width: 3;
    &.no-stroke {
      fill: @fill-lightest;
      stroke-width: 0;
    }
  }
  &.d3-bolus {
    stroke: @bolus;
  }
}

g.svg-tooltip-timechange {
  polygon {
    fill: none;
    stroke-width: 3;
    stroke: darken(@text, 10%);
    &.no-stroke {
      fill: @fill-lightest;
      stroke-width: 0;
    }
  }
}

g.svg-tooltip-message {
  polygon {
    fill: none;
    stroke-width: 3;
    stroke: darken(@message, 10%);
    &.no-stroke {
      fill: @fill-lightest;
      stroke-width: 0;
    }
  }
}