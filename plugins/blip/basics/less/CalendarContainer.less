@import '../../../../css/tideline-breakpoints.less';
@calendar-label-color: @light-gray-color;

.corner-dates {
	position: absolute;
	top: 0;
	left: 0;
	padding-left: @small-pad;
  padding-top: @small-pad/4;
  z-index: 1;
}

.Calendar-weekday() {
	color: white;
}

.Calendar-container {

	.Calendar {

    > div {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      grid-gap: 1em;
      overflow: hidden;

      @media (max-width: @screen-sm-max) {
        grid-gap: .5em;
      }

      @media (min-width: @screen-md-min) and (max-width: @screen-md-max) {
        grid-gap: .75em;
      }

      > div {
        display: flex;
        align-items: flex-start;
        justify-content: center;
        margin-left: 0;
      }
    }

		.Calendar-day--HOVER {
      cursor: pointer;
      display: flex;
		}

		.Calendar-day, .Calendar-day-out-of-range, .Calendar-day--HOVER {
			position: relative;
      color: @calendar-label-color;

      &::before {
        content: '';
        width: 0;
        padding-bottom: 100%;
        grid-row: 1 / 1;
        grid-column: 1 / 1;
      }

			.Calendar-weekday, .Calendar-dayofweek {
				.corner-dates();
				font-size: @small-text;
				margin: 0;
			}

			.Calendar-monthlabel {
				font-weight: bold;
			}
		}

		.Calendar-day-label {
      justify-self: flex-start;
      padding-left: .5em;
      font-size: @small-text;
      color: @calendar-label-color;
		}

		.Calendar-day--fingersticks {
			&.Calendar-day--HOVER {
				background-color: @fingerstick;
				.Calendar-weekday();
			}

			.WrapCount svg circle {
				fill: @fingerstick;
			}
		}

		.Calendar-day--boluses {
			&.Calendar-day--HOVER {
				background-color: @bolus;
				.Calendar-weekday();
			}

			circle {
				fill: @bolus;
			}
		}

		.Calendar-day--basals {
			&.Calendar-day--HOVER {
				background-color: @basal;
				.Calendar-weekday();
			}

			circle {
				fill: @basal;
			}
		}

    .Calendar-day--reservoirChange, .Calendar-day--tubingPrime, .Calendar-day--cannulaPrime {
			&.Calendar-day--HOVER {
				background-color: @infusion-day-color;
			}
 		}

		.Calendar-day--HOVER {
      align-content: center;
      justify-content: center;
      position: relative;
      display: flex;

			.Calendar-weekday {
				.corner-dates();
				font-weight: bold;
			}

      &.Calendar-day--reservoirChange, &.Calendar-day--tubingPrime, &.Calendar-day--cannulaPrime {
				color: @text;
      }

			.Calendar-day-text {
        align-self: center;
        flex-grow: 1;
        width: 100%;
				text-align: center;
				font-size: 20px;
				font-weight: bold;
				color: white;
			}

			.Calendar-day-reservoirChange-times {
        .corner-dates();
				top: (@day-height/3.5);
				color: @text;
				width: 100%;
				font-size: 13px;
				line-height: 16px;
				list-style-type: none;
				padding: 0px;
        margin: 0px;

				li {
					padding: 0px;
					margin: 0px 5px;
				}
			}
		}

		.Calendar-day-out-of-range {
			background-color: @out-of-range-day-color;
		}

		.Calendar-day-most-recent {
			overflow-x: hidden;
		}

		.Calendar-day {
      background-color: @day-color;

			.WrapCount {
        margin-top: 23%;

				svg {
          float: left;
          display: block;
          margin: 3px;

          &:nth-child(4),
          &:nth-child(7) {
            clear: left
          }
				}

				.NestedCount {
          float: left;
          position: relative;
          left: 2px;
          top: 2px;

          svg {
            float: left;
            display: block;
            margin: 1px;


            &:nth-child(4),
            &:nth-child(7) {
              clear: left
            }
          }

				}
			}
		}

		.Calendar-day--disabled {
			background-color: @white-color;
			border: 1px solid @light-gray-color;
		}
	}
}
