/**
 * Copyright (c) 2015, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of ME<PERSON><PERSON>NTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

// Icons generated with fontello.com

[class^="icon-"]:before, [class*=" icon-"]:before {
  font-family: "Blip Icons";
  font-style: normal;
  font-weight: normal;
  speak: none;

  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */

  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;

  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;

  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;

  /* you can be more comfortable with increased icons size */
  font-size: 110%;

  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.icon-upload:before { content: '\e800'; } /* '' */
.icon-up:before { content: '\e801'; } /* '' */
.icon-unsure-data:before { content: '\e802'; } /* '' */
.icon-refresh:before { content: '\e804'; } /* '' */
.icon-profile:before { content: '\e805'; } /* '' */
.icon-next:before { content: '\e806'; } /* '' */
.icon-next-up:before { content: '\e807'; } /* '' */
.icon-most-recent:before { content: '\e808'; } /* '' */
.icon-most-recent-up:before { content: '\e809'; } /* '' */
.icon-logout:before { content: '\e80a'; } /* '' */
.icon-down:before { content: '\e80b'; } /* '' */
.icon-close:before { content: '\e80c'; } /* '' */
.icon-careteam:before { content: '\e80d'; } /* '' */
.icon-back:before { content: '\e80e'; } /* '' */
.icon-back-down:before { content: '\e80f'; } /* '' */
.icon-add:before { content: '\e810'; } /* '' */
.icon-right:before { content: '\e811'; } /* '' */

@media print {
  .icon-back, .icon-next, .icon-next-up, .icon-back-down {
    display: none;
  }
}
