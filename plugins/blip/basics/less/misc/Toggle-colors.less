@gray: lighten(@text, 10%);

.react-toggle-track {
  background-color: @gray;
}

.react-toggle:hover .react-toggle-track {
  background-color: @gray;
}

.react-toggle--checked .react-toggle-track {
  background-color: @gray;
}

.react-toggle.react-toggle--checked:hover .react-toggle-track {
  background-color: @gray;
}

.react-toggle-thumb {
  border: 1px solid @gray;
  background-color: #FAFAFA;
}

.react-toggle--checked .react-toggle-thumb {
  left: 27px;
  border-color: @gray;
}
